package com.flutterup.app.screen.profile

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.wheelpicker.DateFormatters
import com.flutterup.app.design.component.wheelpicker.SimpleDateWheelPicker
import com.flutterup.app.design.theme.AppTheme
import java.time.LocalDate

@Composable
fun BirthdayDialog(
    isShown: Boolean,
    defaultDate: LocalDate?,
    onDismissRequest: () -> Unit,
    onBirthdayConfirmRequest: (birthday: LocalDate?) -> Unit,
) {
    BirthdayDialogContent(
        isShown = isShown,
        defaultDate = defaultDate,
        onDismissRequest = onDismissRequest,
        onBirthdayConfirmRequest = onBirthdayConfirmRequest,
    )
}

@Composable
private fun BirthdayDialogContent(
    isShown: Boolean,
    defaultDate: LocalDate? = null,
    onDismissRequest: () -> Unit,
    onBirthdayConfirmRequest: (birthday: LocalDate?) -> Unit,
) {
    var currentBirthday by remember { mutableStateOf(defaultDate) }

    AppBottomSheetDialog(
        isShown = isShown,
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        onDismissRequest = onDismissRequest,
        onConfirmRequest = {
            onBirthdayConfirmRequest(currentBirthday)
        },
        containerPaddingValues = PaddingValues(horizontal = 20.dp, vertical = 35.dp),
        dragHandle = {}
    ) {
       
    }
}

@Preview
@Composable
private fun BirthdayDialogPreview() {
    AppTheme {
        BirthdayDialogContent(
            isShown = true,
            onDismissRequest = {},
            onBirthdayConfirmRequest = {},
        )
    }
}

private val DEFAULT_CENTER_YEAR = LocalDate.now().minusYears(25)
private val DEFAULT_START_YEAR = LocalDate.now().minusYears(70)
private val DEFAULT_END_YEAR = LocalDate.now().minusYears(18)
private val DEFAULT_RANGE = DEFAULT_START_YEAR.year..DEFAULT_END_YEAR.year
